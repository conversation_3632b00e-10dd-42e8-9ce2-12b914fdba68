import { FilterSchema } from "../../../../shared/infrastructure/http/criteria-utils";

export const getServicesFilterSchema: Array<FilterSchema> = [
	{ field: "id", type: "integer" },
	{ field: "periodicity", type: "integer" },
	{ field: "start_date", type: "date" },
	{ field: "end_date", type: "date" },
	{ field: "capacity", type: "integer" },
	{ field: "active", type: "boolean" },
	{ field: "price", type: "float" },
	{ field: "max_reserve_days", type: "integer" },
	{ field: "created_at", type: "date" },
	{ field: "updated_at", type: "date" },
	{ field: "lang_code", type: "string" },
	{ field: "removed", type: "boolean" },
	{ field: "service", type: "string" },
	{ field: "short_description", type: "string" },
	{ field: "description", type: "string" },
	{ field: "image", type: "string" },
	{ field: "meeting_address", type: "string" },
	{ field: "address", type: "string" },
	{ field: "priority", type: "integer" },
];

export const removeServicesFilterSchema: Array<FilterSchema> = [
	{ field: "id", type: "integer" },
	{ field: "start_hour", type: "string" },
	{ field: "end_hour", type: "string" },
	{ field: "periodicity", type: "integer" },
	{ field: "start_date", type: "date" },
	{ field: "capacity", type: "integer" },
	{ field: "active", type: "boolean" },
	{ field: "price", type: "float" },
	{ field: "max_reserve_days", type: "integer" },
	{ field: "created_at", type: "date" },
	{ field: "updated_at", type: "date" },
];

export const getServiceBookingsSchema: Array<FilterSchema> = [
	{ field: "id", type: "integer" },
	{ field: "customer_id", type: "integer" },
	{ field: "payment_id", type: "integer" },
	{ field: "reference", type: "string" },
	{ field: "reserve_time", type: "date" },
	{ field: "booking_end_time", type: "date" },
	{ field: "quantity", type: "integer" },
	{ field: "price", type: "float" },
	{ field: "created_at", type: "date" },
	{ field: "updated_at", type: "date" },
];

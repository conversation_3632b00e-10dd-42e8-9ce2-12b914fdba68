{"ast": null, "code": "import _asyncToGenerator from \"C:/projects/restaurant-viva-la-pepa-backoffice/backoffice-frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { DataTableDirective } from \"angular-datatables\";\nimport { commonDTSettings } from \"../../../../../shared/infrastructure/datatables/common-settings\";\nimport * as moment from \"moment\";\nimport { CustomerId } from \"../../../domain/value-objects/CustomerId\";\nimport { SetEditingCustomerAction } from \"../../../app/SetEditingCustomerAction\";\nimport { CustomerFormComponent } from \"../customer-form/customer-form.component\";\nimport { RemoveCustomersAction } from \"../../../app/RemoveCustomersAction\";\nimport { ByCustomerId } from \"../../../domain/criteria/ByCustomerId\";\nimport { CustomerEventEmitter } from \"../../../domain/events/CustomerEventEmitter\";\nimport { RemoveEditingCustomerAction } from \"../../../app/RemoveEditingCustomerAction\";\nimport { deleteResp } from \"src/app/shared/infrastructure/subscriptions-handlers\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngxs/store\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"../../services/api-customer-service/api-customer.service\";\nimport * as i4 from \"angular-datatables\";\nexport class CustomersTableComponent {\n  constructor(renderer, store, ngbModal, customerService) {\n    this.renderer = renderer;\n    this.store = store;\n    this.ngbModal = ngbModal;\n    this.customerService = customerService;\n    this.dtOptions = {\n      ...commonDTSettings,\n      order: [[7, 'desc']],\n      columns: [{\n        title: \"Acciones\",\n        data: \"id\",\n        className: 'col-1'\n      }, {\n        title: \"Nombre\",\n        data: \"firstname\"\n      }, {\n        title: \"Apellidos\",\n        data: \"lastname\"\n      }, {\n        title: \"Email\",\n        data: \"email\",\n        className: 'col-2'\n      }, {\n        title: \"Teléfono\",\n        data: \"phone\",\n        className: 'col-2 text-start'\n      }, {\n        title: \"ID Usuario GuestPro\",\n        data: \"external_id\",\n        visible: false\n      }, {\n        title: \"ID Reserva GuestPro\",\n        data: \"booking_id\",\n        visible: false\n      }, {\n        title: \"Fecha de creación\",\n        data: \"created_at\",\n        className: 'col-1'\n      }],\n      //dom: 'Bflrtip',\n      dom: '<\"row align-items-end\"<\"col-6 \"f><\"col-6 col-lg-6 datatab-search mb-3\"l><\"col-6 col-lg-6 \"B>>t<\"row mt-2\"<\" col-md-6\"i><\"col-md-6\"p>>',\n      lengthMenu: [[25, 9999], [25, 'Todos']],\n      buttons: [{\n        extend: 'excelHtml5',\n        filename: 'vivalapepa_clientes_' + moment().format('DDMMYYYY_HHmmss'),\n        text: 'Exportar excel<img src=\"../../../../../../assets/images/export-icon.png\" alt=\"\" class=\"ps-2\">',\n        className: 'bg-white text-secondary fw-semibold',\n        exportOptions: {\n          columns: [1, 2, 3, 6] // index of columns to export\n        }\n      }],\n\n      ajax: (datatablesParameters, callback) => {\n        this.customerService.datatables(datatablesParameters).subscribe(callback);\n      },\n      columnDefs: [{\n        targets: [0],\n        render: (data, type, row) => {\n          return `\n              <div class=\"d-flex gap-2\">\n                <button class=\"btn btn-sm btn-primary action-btn d-flex justify-content-center rounded-circle p-auto border border-0\" data-edit-action=\"edit\" data-row-id=\"${row.id}\"\">\n                    <img data-edit-action=\"edit\" data-row-id=\"${row.id}\" alt=\"\" class=\"mx-auto d-block\" src=\"./assets/images/edit-icon.png\" style=\"width: 12px; heigth: 12px;\">\n                </button>\n                <button class=\"btn btn-sm btn-danger action-btn d-flex justify-content-center rounded-circle border border-0\" data-edit-action=\"delete\" data-row-id=\"${row.id}\">\n                    <img data-edit-action=\"delete\" data-row-id=\"${row.id}\" alt=\"\" class=\"mx-auto d-block\" src=\"./assets/images/close-icon.png\" style=\"width: 14px; heigth: 14px;\">\n                </button>\n              </div>\n            `;\n        }\n      }, {\n        targets: [7],\n        render: (date, type, row) => {\n          /* return `\r\n                        <i class=\"fa-solid fa-calendar-days\"></i>\r\n                        ${moment(date).format(\"DD/MM/YYYY\")}\r\n                        <i class=\"fa-solid fa-clock ms-1\"></i>\r\n                        ${moment(date).format(\"HH:mm\")}\r\n                    `; */\n          return `\n\t\t\t\t\t\t${moment(date).format(\"DD/MM/YYYY\")}\n\t\t\t\t\t`;\n        }\n      }]\n    };\n    this.listenerDestroyers = new Array();\n    this.subscriptions = new Array();\n  }\n  ngOnInit() {\n    if (this.dtOptions.language !== undefined) {\n      this.dtOptions.language.searchPlaceholder = '';\n    }\n    this.subscriptions.push(CustomerEventEmitter.customerRemoved.listen(() => this.refreshDatatable()), CustomerEventEmitter.customerUpdated.listen(() => this.refreshDatatable()), CustomerEventEmitter.customerSaved.listen(() => this.refreshDatatable()));\n  }\n  ngAfterViewInit() {\n    const listenerDestroyer = this.renderer.listen(\"document\", \"click\", event => {\n      if (!event.target.hasAttribute(\"data-row-id\")) {\n        return;\n      }\n      const actionType = event.target.getAttribute(\"data-edit-action\");\n      const userID = event.target.getAttribute(\"data-row-id\");\n      switch (actionType) {\n        case \"edit\":\n          this.editCustomer(new CustomerId(parseInt(userID)));\n          break;\n        case \"delete\":\n          this.deleteCustomer(new CustomerId(parseInt(userID)));\n          break;\n      }\n    });\n    this.listenerDestroyers.push(listenerDestroyer);\n  }\n  editCustomer(userID) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.subscriptions.push(_this.store.dispatch(new SetEditingCustomerAction(userID, _this.customerService)).subscribe(() => {\n        const modalRef = _this.ngbModal.open(CustomerFormComponent, {\n          backdrop: 'static',\n          size: 'lg'\n        });\n        _this.subscriptions.push(modalRef.closed.subscribe(() => _this.store.dispatch(new RemoveEditingCustomerAction())), modalRef.dismissed.subscribe(() => _this.store.dispatch(new RemoveEditingCustomerAction())));\n      }));\n    })();\n  }\n  deleteCustomer(userID) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      /* const response = await Swal.fire({\r\n        icon: \"warning\",\r\n        title: \"Estás seguro?\",\r\n        html: \"Seguro que quieres eliminar este registro?\",\r\n        confirmButtonText: \"Sí\",\r\n        cancelButtonText: `Cancelar`,\r\n        confirmButtonColor: \"#BEC76F\",\r\n        cancelButtonColor: \"#3d3d3d\",\r\n        showCancelButton: true,\r\n      });\r\n      */\n      deleteResp(); //show popup\n      if (!(yield deleteResp()).isConfirmed) {\n        return;\n      }\n      _this2.store.dispatch(new RemoveCustomersAction(new ByCustomerId(userID), _this2.customerService));\n    })();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.listenerDestroyers.forEach(destroyer => destroyer());\n  }\n  refreshDatatable() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const dtInstance = yield _this3.datatableElement.dtInstance;\n      dtInstance.ajax.reload();\n    })();\n  }\n}\nCustomersTableComponent.ɵfac = function CustomersTableComponent_Factory(t) {\n  return new (t || CustomersTableComponent)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i1.Store), i0.ɵɵdirectiveInject(i2.NgbModal), i0.ɵɵdirectiveInject(i3.ApiCustomerService));\n};\nCustomersTableComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: CustomersTableComponent,\n  selectors: [[\"app-customers-table\"]],\n  viewQuery: function CustomersTableComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(DataTableDirective, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.datatableElement = _t.first);\n    }\n  },\n  decls: 1,\n  vars: 1,\n  consts: [[\"datatable\", \"\", 1, \"table\", \"table-striped\", \"w-100\", \"m-custom\", 3, \"dtOptions\"]],\n  template: function CustomersTableComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelement(0, \"table\", 0);\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"dtOptions\", ctx.dtOptions);\n    }\n  },\n  dependencies: [i4.DataTableDirective],\n  styles: [\".m-custom {\\n  margin-top: 2rem !important;\\n}\\n\\n@media (min-width: 992px) {\\n    .m-custom {\\n    margin-top: auto !important;\\n    margin-bottom: auto !important;\\n  }\\n}\\n@media (min-width: 768px) {\\n    .m-custom {\\n    margin-top: 1.5rem !important;\\n    margin-bottom: 1.5rem !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvY3VzdG9tZXJzL2luZnJhc3RydWN0dXJlL2NvbXBvbmVudHMvY3VzdG9tZXJzLXRhYmxlL2N1c3RvbWVycy10YWJsZS5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLDJCQUFBO0FBQ0Y7O0FBRUE7RUFDRTtJQUNFLDJCQUFBO0lBQ0EsOEJBQUE7RUFDRjtBQUNGO0FBRUE7RUFDRTtJQUNFLDZCQUFBO0lBQ0EsZ0NBQUE7RUFBRjtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiOjpuZy1kZWVwIC5tLWN1c3RvbSB7XHJcbiAgbWFyZ2luLXRvcDogMnJlbSAhaW1wb3J0YW50O1xyXG59XHJcblxyXG5AbWVkaWEgKG1pbi13aWR0aDogOTkycHgpIHtcclxuICA6Om5nLWRlZXAgLm0tY3VzdG9tIHtcclxuICAgIG1hcmdpbi10b3A6IGF1dG8gIWltcG9ydGFudDtcclxuICAgIG1hcmdpbi1ib3R0b206IGF1dG8gIWltcG9ydGFudDtcclxuICB9XHJcbn1cclxuXHJcbkBtZWRpYSAobWluLXdpZHRoOiA3NjhweCkge1xyXG4gIDo6bmctZGVlcCAubS1jdXN0b20ge1xyXG4gICAgbWFyZ2luLXRvcDogMS41cmVtICFpbXBvcnRhbnQ7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAxLjVyZW0gIWltcG9ydGFudDtcclxuICB9XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n});", "map": {"version": 3, "mappings": ";AACA,SAASA,kBAAkB,QAAQ,oBAAoB;AAEvD,SAASC,gBAAgB,QAAQ,iEAAiE;AAClG,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAGhC,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,wBAAwB,QAAQ,uCAAuC;AAChF,SAASC,qBAAqB,QAAQ,0CAA0C;AAEhF,SAASC,qBAAqB,QAAQ,oCAAoC;AAC1E,SAASC,YAAY,QAAQ,uCAAuC;AAEpE,SAASC,oBAAoB,QAAQ,6CAA6C;AAClF,SAASC,2BAA2B,QAAQ,0CAA0C;AACtF,SAASC,UAAU,QAAQ,sDAAsD;;;;;;AAOjF,OAAM,MAAOC,uBAAuB;EAqElCC,YACUC,QAAmB,EACnBC,KAAY,EACZC,QAAkB,EAClBC,eAAmC;IAHnC,aAAQ,GAARH,QAAQ;IACR,UAAK,GAALC,KAAK;IACL,aAAQ,GAARC,QAAQ;IACR,oBAAe,GAAfC,eAAe;IAtEzB,cAAS,GAAQ;MACf,GAAGf,gBAAgB;MACnBgB,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;MACpBC,OAAO,EAAE,CACP;QAAEC,KAAK,EAAE,UAAU;QAAEC,IAAI,EAAE,IAAI;QAAEC,SAAS,EAAE;MAAO,CAAE,EACrD;QAAEF,KAAK,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAW,CAAE,EACtC;QAAED,KAAK,EAAE,WAAW;QAAEC,IAAI,EAAE;MAAU,CAAE,EACxC;QAAED,KAAK,EAAE,OAAO;QAAEC,IAAI,EAAE,OAAO;QAAEC,SAAS,EAAE;MAAO,CAAE,EACrD;QAAEF,KAAK,EAAE,UAAU;QAAEC,IAAI,EAAE,OAAO;QAAEC,SAAS,EAAE;MAAkB,CAAE,EACnE;QAAEF,KAAK,EAAE,qBAAqB;QAAEC,IAAI,EAAE,aAAa;QAAEE,OAAO,EAAE;MAAK,CAAE,EACrE;QAAEH,KAAK,EAAE,qBAAqB;QAAEC,IAAI,EAAE,YAAY;QAAEE,OAAO,EAAE;MAAK,CAAE,EACpE;QAAEH,KAAK,EAAE,mBAAmB;QAAEC,IAAI,EAAE,YAAY;QAAEC,SAAS,EAAE;MAAO,CAAE,CACvE;MACA;MACAE,GAAG,EAAE,uIAAuI;MAC7IC,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;MACvCC,OAAO,EAAE,CACP;QACEC,MAAM,EAAE,YAAY;QACpBC,QAAQ,EAAE,sBAAsB,GAAGzB,MAAM,EAAE,CAAC0B,MAAM,CAAC,iBAAiB,CAAC;QACrEC,IAAI,EAAE,+FAA+F;QACrGR,SAAS,EAAE,qCAAqC;QAChDS,aAAa,EAAE;UACbZ,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;;OAEzB,CACF;;MACDa,IAAI,EAAE,CAACC,oBAAyB,EAAEC,QAAa,KAAI;QACjD,IAAI,CAACjB,eAAe,CAACkB,UAAU,CAACF,oBAAoB,CAAC,CAACG,SAAS,CAACF,QAAQ,CAAC;MAC3E,CAAC;MACDG,UAAU,EAAE,CACV;QACEC,OAAO,EAAE,CAAC,CAAC,CAAC;QACZC,MAAM,EAAE,CAAClB,IAAS,EAAEmB,IAAS,EAAEC,GAAQ,KAAI;UACzC,OAAO;;6KAE4JA,GAAG,CAACC,EAAE;gEACnHD,GAAG,CAACC,EAAE;;uKAEiGD,GAAG,CAACC,EAAE;kEAC3GD,GAAG,CAACC,EAAE;;;aAG3D;QACL;OACD,EAED;QACEJ,OAAO,EAAE,CAAC,CAAC,CAAC;QACZC,MAAM,EAAE,CAACI,IAAS,EAAEH,IAAS,EAAEC,GAAQ,KAAI;UACzC;;;;;;UAMA,OAAO;QACTtC,MAAM,CAACwC,IAAI,CAAC,CAACd,MAAM,CAAC,YAAY,CAAC;MACnC;QACE;OACD;KAEJ;IACO,uBAAkB,GAAG,IAAIe,KAAK,EAAc;IAC5C,kBAAa,GAAG,IAAIA,KAAK,EAAgB;EAO9C;EAEHC,QAAQ;IACL,IAAI,IAAI,CAACC,SAAS,CAACC,QAAQ,KAAKC,SAAS,EAAE;MAC1C,IAAI,CAACF,SAAS,CAACC,QAAQ,CAACE,iBAAiB,GAAG,EAAE;;IAEhD,IAAI,CAACC,aAAa,CAACC,IAAI,CACrB1C,oBAAoB,CAAC2C,eAAe,CAACC,MAAM,CAAC,MAAM,IAAI,CAACC,gBAAgB,EAAE,CAAC,EAC1E7C,oBAAoB,CAAC8C,eAAe,CAACF,MAAM,CAAC,MAAM,IAAI,CAACC,gBAAgB,EAAE,CAAC,EAC1E7C,oBAAoB,CAAC+C,aAAa,CAACH,MAAM,CAAC,MAAM,IAAI,CAACC,gBAAgB,EAAE,CAAC,CACzE;EACH;EAEAG,eAAe;IACb,MAAMC,iBAAiB,GAAG,IAAI,CAAC5C,QAAQ,CAACuC,MAAM,CAAC,UAAU,EAAE,OAAO,EAAGM,KAAK,IAAI;MAC5E,IAAI,CAACA,KAAK,CAACC,MAAM,CAACC,YAAY,CAAC,aAAa,CAAC,EAAE;QAC7C;;MAGF,MAAMC,UAAU,GAAGH,KAAK,CAACC,MAAM,CAACG,YAAY,CAAC,kBAAkB,CAAC;MAChE,MAAMC,MAAM,GAAGL,KAAK,CAACC,MAAM,CAACG,YAAY,CAAC,aAAa,CAAC;MAEvD,QAAQD,UAAU;QAChB,KAAK,MAAM;UACT,IAAI,CAACG,YAAY,CAAC,IAAI7D,UAAU,CAAC8D,QAAQ,CAACF,MAAM,CAAC,CAAC,CAAC;UACnD;QACF,KAAK,QAAQ;UACX,IAAI,CAACG,cAAc,CAAC,IAAI/D,UAAU,CAAC8D,QAAQ,CAACF,MAAM,CAAC,CAAC,CAAC;UACrD;MAAM;IAEZ,CAAC,CAAC;IAEF,IAAI,CAACI,kBAAkB,CAACjB,IAAI,CAACO,iBAAiB,CAAC;EACjD;EAEMO,YAAY,CAACD,MAAkB;IAAA;IAAA;MACnC,KAAI,CAACd,aAAa,CAACC,IAAI,CACrB,KAAI,CAACpC,KAAK,CACPsD,QAAQ,CAAC,IAAIhE,wBAAwB,CAAC2D,MAAM,EAAE,KAAI,CAAC/C,eAAe,CAAC,CAAC,CACpEmB,SAAS,CAAC,MAAK;QACd,MAAMkC,QAAQ,GAAG,KAAI,CAACtD,QAAQ,CAACuD,IAAI,CAACjE,qBAAqB,EAAE;UAACkE,QAAQ,EAAE,QAAQ;UAAEC,IAAI,EAAE;QAAI,CAAC,CAAC;QAC5F,KAAI,CAACvB,aAAa,CAACC,IAAI,CACrBmB,QAAQ,CAACI,MAAM,CAACtC,SAAS,CAAC,MAAM,KAAI,CAACrB,KAAK,CAACsD,QAAQ,CAAC,IAAI3D,2BAA2B,EAAE,CAAC,CAAC,EACvF4D,QAAQ,CAACK,SAAS,CAACvC,SAAS,CAAC,MAC3B,KAAI,CAACrB,KAAK,CAACsD,QAAQ,CAAC,IAAI3D,2BAA2B,EAAE,CAAC,CACvD,CACF;MACH,CAAC,CAAC,CACL;IAAC;EACJ;EAEMyD,cAAc,CAACH,MAAkB;IAAA;IAAA;MACrC;;;;;;;;;;;MAYArD,UAAU,EAAE,CAAC,CAAC;MACd,IAAI,CAAC,OAAOA,UAAU,EAAE,EAAEiE,WAAW,EAAE;QACrC;;MAGF,MAAI,CAAC7D,KAAK,CAACsD,QAAQ,CAAC,IAAI9D,qBAAqB,CAAC,IAAIC,YAAY,CAACwD,MAAM,CAAC,EAAE,MAAI,CAAC/C,eAAe,CAAC,CAAC;IAAC;EACjG;EAEA4D,WAAW;IACT,IAAI,CAAC3B,aAAa,CAAC4B,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAACC,WAAW,EAAE,CAAC;IACtD,IAAI,CAACZ,kBAAkB,CAACU,OAAO,CAAEG,SAAS,IAAKA,SAAS,EAAE,CAAC;EAC7D;EAEc3B,gBAAgB;IAAA;IAAA;MAC5B,MAAM4B,UAAU,SAAS,MAAI,CAACC,gBAAgB,CAACD,UAAU;MACzDA,UAAU,CAAClD,IAAI,CAACoD,MAAM,EAAE;IAAC;EAC3B;;AA1JWxE,uBAAuB;mBAAvBA,uBAAuB;AAAA;AAAvBA,uBAAuB;QAAvBA,uBAAuB;EAAAyE;EAAAC;IAAA;qBACvBrF,kBAAkB;;;;;;;;;;;;MCxB/BsF,2BAA4F;;;MAArFA,yCAAuB", "names": ["DataTableDirective", "commonDTSettings", "moment", "CustomerId", "SetEditingCustomerAction", "CustomerFormComponent", "RemoveCustomersAction", "ByCustomerId", "CustomerEventEmitter", "RemoveEditingCustomerAction", "deleteResp", "CustomersTableComponent", "constructor", "renderer", "store", "ngbModal", "customerService", "order", "columns", "title", "data", "className", "visible", "dom", "lengthMenu", "buttons", "extend", "filename", "format", "text", "exportOptions", "ajax", "datatablesParameters", "callback", "datatables", "subscribe", "columnDefs", "targets", "render", "type", "row", "id", "date", "Array", "ngOnInit", "dtOptions", "language", "undefined", "searchPlaceholder", "subscriptions", "push", "customerRemoved", "listen", "refreshDatatable", "customerUpdated", "customerSaved", "ngAfterViewInit", "listener<PERSON><PERSON><PERSON><PERSON>", "event", "target", "hasAttribute", "actionType", "getAttribute", "userID", "editCustomer", "parseInt", "deleteCustomer", "listenerDestroyers", "dispatch", "modalRef", "open", "backdrop", "size", "closed", "dismissed", "isConfirmed", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "destroyer", "dtInstance", "datatableElement", "reload", "selectors", "viewQuery", "i0"], "sourceRoot": "", "sources": ["C:\\projects\\restaurant-viva-la-pepa-backoffice\\backoffice-frontend\\src\\app\\features\\customers\\infrastructure\\components\\customers-table\\customers-table.component.ts", "C:\\projects\\restaurant-viva-la-pepa-backoffice\\backoffice-frontend\\src\\app\\features\\customers\\infrastructure\\components\\customers-table\\customers-table.component.html"], "sourcesContent": ["import { <PERSON><PERSON>iewInit, Component, OnInit, Renderer2, ViewChild } from \"@angular/core\";\r\nimport { DataTableDirective } from \"angular-datatables\";\r\nimport { Subscription } from \"rxjs\";\r\nimport { commonDTSettings } from \"../../../../../shared/infrastructure/datatables/common-settings\";\r\nimport * as moment from \"moment\";\r\nimport { Store } from \"@ngxs/store\";\r\nimport { NgbModal } from \"@ng-bootstrap/ng-bootstrap\";\r\nimport { CustomerId } from \"../../../domain/value-objects/CustomerId\";\r\nimport { SetEditingCustomerAction } from \"../../../app/SetEditingCustomerAction\";\r\nimport { CustomerFormComponent } from \"../customer-form/customer-form.component\";\r\nimport Swal from \"sweetalert2\";\r\nimport { RemoveCustomersAction } from \"../../../app/RemoveCustomersAction\";\r\nimport { ByCustomerId } from \"../../../domain/criteria/ByCustomerId\";\r\nimport { ApiCustomerService } from \"../../services/api-customer-service/api-customer.service\";\r\nimport { CustomerEventEmitter } from \"../../../domain/events/CustomerEventEmitter\";\r\nimport { RemoveEditingCustomerAction } from \"../../../app/RemoveEditingCustomerAction\";\r\nimport { deleteResp } from \"src/app/shared/infrastructure/subscriptions-handlers\";\r\n\r\n@Component({\r\n  selector: 'app-customers-table',\r\n  templateUrl: './customers-table.component.html',\r\n  styleUrls: ['./customers-table.component.scss']\r\n})\r\nexport class CustomersTableComponent implements OnInit, AfterViewInit {\r\n  @ViewChild(DataTableDirective, {static: false})\r\n  datatableElement!: DataTableDirective;\r\n  dtOptions: any = {\r\n    ...commonDTSettings,\r\n    order: [[7, 'desc']],\r\n    columns: [\r\n      { title: \"Acciones\", data: \"id\", className: 'col-1' },\r\n      { title: \"Nombre\", data: \"firstname\" },\r\n      { title: \"Apellidos\", data: \"lastname\" },\r\n      { title: \"Email\", data: \"email\", className: 'col-2' },\r\n      { title: \"Teléfono\", data: \"phone\", className: 'col-2 text-start' },\r\n      { title: \"ID Usuario GuestPro\", data: \"external_id\", visible: false },\r\n      { title: \"ID Reserva GuestPro\", data: \"booking_id\", visible: false },\r\n      { title: \"Fecha de creación\", data: \"created_at\", className: 'col-1' },\r\n    ],\r\n     //dom: 'Bflrtip',\r\n     dom: '<\"row align-items-end\"<\"col-6 \"f><\"col-6 col-lg-6 datatab-search mb-3\"l><\"col-6 col-lg-6 \"B>>t<\"row mt-2\"<\" col-md-6\"i><\"col-md-6\"p>>',\r\n    lengthMenu: [[25, 9999], [25, 'Todos']],\r\n    buttons: [\r\n      {\r\n        extend: 'excelHtml5',\r\n        filename: 'vivalapepa_clientes_' + moment().format('DDMMYYYY_HHmmss'),\r\n        text: 'Exportar excel<img src=\"../../../../../../assets/images/export-icon.png\" alt=\"\" class=\"ps-2\">',\r\n        className: 'bg-white text-secondary fw-semibold',\r\n        exportOptions: {\r\n          columns: [1, 2, 3, 6] // index of columns to export\r\n        }\r\n      }\r\n    ],\r\n    ajax: (datatablesParameters: any, callback: any) => {\r\n      this.customerService.datatables(datatablesParameters).subscribe(callback);\r\n    },\r\n    columnDefs: [\r\n      {\r\n        targets: [0],\r\n        render: (data: any, type: any, row: any) => {\r\n          return `\r\n              <div class=\"d-flex gap-2\">\r\n                <button class=\"btn btn-sm btn-primary action-btn d-flex justify-content-center rounded-circle p-auto border border-0\" data-edit-action=\"edit\" data-row-id=\"${row.id}\"\">\r\n                    <img data-edit-action=\"edit\" data-row-id=\"${row.id}\" alt=\"\" class=\"mx-auto d-block\" src=\"./assets/images/edit-icon.png\" style=\"width: 12px; heigth: 12px;\">\r\n                </button>\r\n                <button class=\"btn btn-sm btn-danger action-btn d-flex justify-content-center rounded-circle border border-0\" data-edit-action=\"delete\" data-row-id=\"${row.id}\">\r\n                    <img data-edit-action=\"delete\" data-row-id=\"${row.id}\" alt=\"\" class=\"mx-auto d-block\" src=\"./assets/images/close-icon.png\" style=\"width: 14px; heigth: 14px;\">\r\n                </button>\r\n              </div>\r\n            `;\r\n        },\r\n      },\r\n\r\n      {\r\n        targets: [7],\r\n        render: (date: any, type: any, row: any) => {\r\n          /* return `\r\n\t\t\t\t\t\t<i class=\"fa-solid fa-calendar-days\"></i>\r\n\t\t\t\t\t\t${moment(date).format(\"DD/MM/YYYY\")}\r\n\t\t\t\t\t\t<i class=\"fa-solid fa-clock ms-1\"></i>\r\n\t\t\t\t\t\t${moment(date).format(\"HH:mm\")}\r\n\t\t\t\t\t`; */\r\n          return `\r\n\t\t\t\t\t\t${moment(date).format(\"DD/MM/YYYY\")}\r\n\t\t\t\t\t`;\r\n        },\r\n      },\r\n    ],\r\n  };\r\n  private listenerDestroyers = new Array<() => void>();\r\n  private subscriptions = new Array<Subscription>();\r\n\r\n  constructor(\r\n    private renderer: Renderer2,\r\n    private store: Store,\r\n    private ngbModal: NgbModal,\r\n    private customerService: ApiCustomerService,\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n     if (this.dtOptions.language !== undefined) {\r\n      this.dtOptions.language.searchPlaceholder = '';\r\n    }\r\n    this.subscriptions.push(\r\n      CustomerEventEmitter.customerRemoved.listen(() => this.refreshDatatable()),\r\n      CustomerEventEmitter.customerUpdated.listen(() => this.refreshDatatable()),\r\n      CustomerEventEmitter.customerSaved.listen(() => this.refreshDatatable()),\r\n    );\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    const listenerDestroyer = this.renderer.listen(\"document\", \"click\", (event) => {\r\n      if (!event.target.hasAttribute(\"data-row-id\")) {\r\n        return;\r\n      }\r\n\r\n      const actionType = event.target.getAttribute(\"data-edit-action\");\r\n      const userID = event.target.getAttribute(\"data-row-id\");\r\n\r\n      switch (actionType) {\r\n        case \"edit\":\r\n          this.editCustomer(new CustomerId(parseInt(userID)));\r\n          break;\r\n        case \"delete\":\r\n          this.deleteCustomer(new CustomerId(parseInt(userID)));\r\n          break;\r\n      }\r\n    });\r\n\r\n    this.listenerDestroyers.push(listenerDestroyer);\r\n  }\r\n\r\n  async editCustomer(userID: CustomerId) {\r\n    this.subscriptions.push(\r\n      this.store\r\n        .dispatch(new SetEditingCustomerAction(userID, this.customerService))\r\n        .subscribe(() => {\r\n          const modalRef = this.ngbModal.open(CustomerFormComponent, {backdrop: 'static', size: 'lg'});\r\n          this.subscriptions.push(\r\n            modalRef.closed.subscribe(() => this.store.dispatch(new RemoveEditingCustomerAction())),\r\n            modalRef.dismissed.subscribe(() =>\r\n              this.store.dispatch(new RemoveEditingCustomerAction()),\r\n            ),\r\n          );\r\n        }),\r\n    );\r\n  }\r\n\r\n  async deleteCustomer(userID: CustomerId) {\r\n    /* const response = await Swal.fire({\r\n      icon: \"warning\",\r\n      title: \"Estás seguro?\",\r\n      html: \"Seguro que quieres eliminar este registro?\",\r\n      confirmButtonText: \"Sí\",\r\n      cancelButtonText: `Cancelar`,\r\n      confirmButtonColor: \"#BEC76F\",\r\n      cancelButtonColor: \"#3d3d3d\",\r\n      showCancelButton: true,\r\n    });\r\n */\r\n\r\n    deleteResp(); //show popup\r\n    if (!(await deleteResp()).isConfirmed) {\r\n      return;\r\n    }\r\n\r\n    this.store.dispatch(new RemoveCustomersAction(new ByCustomerId(userID), this.customerService));\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\r\n    this.listenerDestroyers.forEach((destroyer) => destroyer());\r\n  }\r\n\r\n  private async refreshDatatable() {\r\n    const dtInstance = await this.datatableElement.dtInstance;\r\n    dtInstance.ajax.reload();\r\n  }\r\n}\r\n", "<table [dtOptions]=\"dtOptions\" class=\"table table-striped w-100 m-custom\" datatable></table>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}